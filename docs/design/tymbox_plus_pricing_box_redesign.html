<!doctype html>
<html lang="cs">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Týmbox – Plus Pricing Box (Redesign)</title>
  <style>
    :root{
      --bg: #0b1020;            /* demo backdrop */
      --card: #ffffff;
      --cardText: #0f172a;      /* slate-900 */
      --muted: #475569;         /* slate-600 */
      --line: #e2e8f0;          /* slate-200 */
      --success: #16a34a;       /* green-600 */
      --ring: rgba(34,197,94,.4);
      --brand1: #22c55e;        /* green-500 */
      --brand2: #16a34a;        /* green-600 */
      --brand3: #059669;        /* green-700 */
      --amber: #f59e0b;         /* amber-500 */
      --shadow: 0 10px 30px rgba(2,6,23,.16), 0 2px 8px rgba(2,6,23,.08);
      --radius-xl: 18px;
      --radius-pill: 999px;
    }

    @media (prefers-color-scheme: dark){
      :root{
        --bg: #0b1020;         
        --card: #0f172a;       /* slate-900 */
        --cardText: #e2e8f0;   /* slate-200 */
        --muted: #94a3b8;      /* slate-400 */
        --line: #1f2937;       /* gray-800 */
        --shadow: 0 10px 30px rgba(0,0,0,.45), 0 2px 8px rgba(0,0,0,.35);
      }
    }

    *{ box-sizing: border-box; }
    body{ margin:0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica Neue, Arial, "Apple Color Emoji", "Segoe UI Emoji"; background: var(--bg); color: var(--cardText);}

    /* Demo page layout */
    .wrap{ min-height: 100svh; display:flex; align-items:center; justify-content:center; padding: 32px; }

    /* Pricing Box */
    .pricing{
      width: 100%; max-width: 420px; background: var(--card); color: var(--cardText);
      border-radius: var(--radius-xl); box-shadow: var(--shadow); border: 1px solid var(--line);
      padding: 22px; position: relative; overflow: clip; isolation: isolate;
    }
    .header{ display:flex; align-items:flex-start; justify-content:space-between; gap:12px; margin-bottom: 14px; }
    .title{ font-weight: 800; letter-spacing: -0.02em; font-size: 20px; display:flex; align-items:center; gap:8px; }
    .title .spark{ font-size: 18px; }
    .badge{ font-weight: 700; font-size: 12px; color: #0b1220; background: linear-gradient(180deg, #fde68a, #f59e0b);
            padding: 6px 10px; border-radius: var(--radius-pill); box-shadow: inset 0 1px 0 rgba(255,255,255,.6), 0 1px 2px rgba(0,0,0,.1);
            white-space: nowrap; }

    .subtitle{ font-size: 14px; color: var(--muted); margin-bottom: 14px; }

    .features{ list-style: none; margin: 0 0 18px 0; padding: 0; display:grid; gap: 10px; }
    .feature{ display:flex; align-items:flex-start; gap: 10px; font-size: 14px; }
    .check{ width: 22px; height: 22px; border-radius: 8px; background: rgba(34,197,94,.15); display:grid; place-items:center; flex: 0 0 22px;
            border: 1px solid rgba(34,197,94,.35); }
    .check svg{ width: 14px; height:14px; fill: var(--success); }

    .priceRow{ display:flex; align-items:baseline; justify-content:space-between; gap:12px; margin: 4px 0 18px; }
    .price{ font-weight: 800; font-size: 28px; letter-spacing:-0.02em; }
    .small{ color: var(--muted); font-size: 12px; }

    .ctaWrap{ display:grid; gap: 8px; }
    .cta{ appearance: none; cursor: pointer; border: none; outline: none; position: relative;
          padding: 14px 18px; border-radius: var(--radius-pill); font-weight: 800; font-size: 16px;
          color: #06260f; background: linear-gradient(92deg, var(--brand1), var(--brand2) 55%, var(--brand3));
          box-shadow: 0 8px 20px rgba(5,150,105,.35), inset 0 1px 0 rgba(255,255,255,.5);
          transition: transform .15s ease, box-shadow .15s ease, filter .15s ease; }
    .cta:hover{ transform: translateY(-1px); filter: saturate(1.05); box-shadow: 0 12px 26px rgba(5,150,105,.45), inset 0 1px 0 rgba(255,255,255,.65); }
    .cta:active{ transform: translateY(0); box-shadow: 0 6px 16px rgba(5,150,105,.35); }
    .cta:focus-visible{ outline: 3px solid var(--ring); outline-offset: 3px; }

    .cta .row{ display:flex; align-items:center; justify-content:center; gap:10px; }
    .cta .arrow{ width: 18px; height: 18px; }

    .promise{ text-align:center; font-size: 12px; color: var(--muted); }

    /* Decorative ribbon */
    .ribbon{ position:absolute; right:-80px; top:-80px; width:180px; height:180px; background: radial-gradient(80px 80px at 50% 50%, rgba(34,197,94,0.26), transparent 70%); transform: rotate(25deg); pointer-events:none; }

    /* Compact mode option */
    .pricing.compact{ max-width: 360px; padding: 18px; }
    .pricing.compact .title{ font-size: 18px; }
    .pricing.compact .price{ font-size: 24px; }
  </style>
</head>
<body>
  <div class="wrap">
    <!-- Pricing card start -->
    <article class="pricing" role="region" aria-labelledby="planTitle">
      <div class="ribbon" aria-hidden="true"></div>

      <header class="header">
        <h3 class="title" id="planTitle">
          <span class="spark" aria-hidden="true">✨</span>
          Plán Plus
        </h3>
        <div class="badge" title="Doporučeno pro týmy">Doporučeno</div>
      </header>

      <p class="subtitle">Šetřete čas, mějte přehled a pošlete si reporty automaticky.</p>

      <ul class="features" aria-label="Co získáte v Planu Plus">
        <li class="feature"><span class="check" aria-hidden="true"> 
          <svg viewBox="0 0 24 24" focusable="false" aria-hidden="true"><path d="M20.285 6.709a1 1 0 0 1 0 1.414l-9.9 9.9a1 1 0 0 1-1.414 0l-5.257-5.257a1 1 0 1 1 1.414-1.414l4.55 4.55 9.192-9.193a1 1 0 0 1 1.415 0z"/></svg>
        </span><span>Rozšířené role uživatelů (zástupce, senior kolega)</span></li>
        <li class="feature"><span class="check" aria-hidden="true"> 
          <svg viewBox="0 0 24 24" focusable="false" aria-hidden="true"><path d="M20.285 6.709a1 1 0 0 1 0 1.414l-9.9 9.9a1 1 0 0 1-1.414 0l-5.257-5.257a1 1 0 1 1 1.414-1.414l4.55 4.55 9.192-9.193a1 1 0 0 1 1.415 0z"/></svg>
        </span><span>Ranní reporty a přehledy na e‑mail</span></li>
        <li class="feature"><span class="check" aria-hidden="true"> 
          <svg viewBox="0 0 24 24" focusable="false" aria-hidden="true"><path d="M20.285 6.709a1 1 0 0 1 0 1.414l-9.9 9.9a1 1 0 0 1-1.414 0l-5.257-5.257a1 1 0 1 1 1.414-1.414l4.55 4.55 9.192-9.193a1 1 0 0 1 1.415 0z"/></svg>
        </span><span>Plánovač schůzek a dovolených</span></li>
        <li class="feature"><span class="check" aria-hidden="true"> 
          <svg viewBox="0 0 24 24" focusable="false" aria-hidden="true"><path d="M20.285 6.709a1 1 0 0 1 0 1.414l-9.9 9.9a1 1 0 0 1-1.414 0l-5.257-5.257a1 1 0 1 1 1.414-1.414l4.55 4.55 9.192-9.193a1 1 0 0 1 1.415 0z"/></svg>
        </span><span>Nové funkce přidáváme pravidelně</span></li>
      </ul>

      <div class="priceRow">
        <div class="price" aria-label="Cena">289 Kč <span class="small">/ měsíc</span></div>
        <div class="small">Zrušíte kdykoliv</div>
      </div>

      <div class="ctaWrap">
        <button class="cta" type="button" aria-label="Upgradovat na Plus za 289 Kč měsíčně">
          <span class="row">👉 <span>Upgradovat na Plus</span>
            <svg class="arrow" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M13.172 12L8.222 7.05l1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/></svg>
          </span>
        </button>
        <div class="promise">30denní záruka vrácení peněz</div>
      </div>
    </article>
    <!-- Pricing card end -->
  </div>

  <!-- Minimal JS demo hook to show a click (replace with real upgrade handler) -->
  <script>
    document.querySelector('.cta').addEventListener('click', () => {
      alert('🔥 Hotovo! Tady byste zavolali váš upgrade flow.');
    });
  </script>
</body>
</html>
